import React, { useState } from "react";
import AuthLayout from "../../layout/AuthLayout";
import { Link } from "react-router-dom";

const SignUp = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = (e) => {
    e.preventDefault();
    // Add registration logic here
    if (!email || !password) {
      setError("Please enter both email and password.");
      return;
    }
    setError("");
    // Proceed with sign up
  };

  return (
    <AuthLayout>
      <h2 className="text-2xl font-bold mb-6 text-center">Sign Up</h2>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">Email</label>
          <input
            type="email"
            className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 bg-white dark:bg-brand-950 dark:text-white"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            autoFocus
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Password</label>
          <input
            type="password"
            className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 bg-white dark:bg-brand-950 dark:text-white"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
        </div>
        {error && <div className="text-red-500 text-sm">{error}</div>}
        <button
          type="submit"
          className="w-full py-2 px-4 bg-brand-500 text-white rounded-lg font-semibold hover:bg-brand-600 transition"
        >
          Sign Up
        </button>
      </form>
      <p className="mt-6 text-center text-sm text-gray-600 dark:text-gray-300">
        Already have an account?{" "}
        <Link
          to="/signin"
          className="text-brand-600 dark:text-brand-400 font-semibold hover:underline"
        >
          Sign In
        </Link>
      </p>
    </AuthLayout>
  );
};

export default SignUp;
