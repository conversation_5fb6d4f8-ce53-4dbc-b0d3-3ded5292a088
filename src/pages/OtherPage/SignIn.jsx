import React, { useState } from "react";
import AuthLayout from "../../layout/AuthLayout";
import { Link, useNavigate } from "react-router-dom";
import api from "../../axios/axiosInstance";
import URL from "../../axios/URl";

const SignIn = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!email || !password) {
      setError("Please enter both email and password.");
      return;
    }
    setError("");
    try {
      const response = await api.post(URL.SIGN_IN, { email, password });
      if (response.data && response.data.token) {
        localStorage.setItem("token", response.data.token);
        navigate("/admin/dashboard");
      } else {
        setError(response.data?.detail || "Invalid credentials.");
      }
    } catch (err) {
      setError(err.response?.data?.detail || err.message || "Sign in failed.");
    }
  };

  return (
    <AuthLayout>
      <h2 className="text-2xl font-bold mb-6 text-center">Sign In</h2>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-sm font-medium mb-2">Email</label>
          <input
            type="email"
            className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 bg-white dark:bg-brand-950 dark:text-white"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            autoFocus
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Password</label>
          <input
            type="password"
            className="w-full px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-brand-500 bg-white dark:bg-brand-950 dark:text-white"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
          />
        </div>
        {error && <div className="text-red-500 text-sm">{error}</div>}
        <button
          type="submit"
          className="w-full py-2 px-4 bg-brand-500 text-white rounded-lg font-semibold hover:bg-brand-600 transition"
        >
          Sign In
        </button>
      </form>
    </AuthLayout>
  );
};

export default SignIn;
