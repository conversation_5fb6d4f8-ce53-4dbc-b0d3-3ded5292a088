import React, { useState, useRef, useEffect } from "react";
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Quote,
  Link,
  Image,
  Eye,
  Code,
  Heading1,
  Heading2,
  Heading3,
  Play,
  RotateCcw,
  RotateCw,
} from "lucide-react";
import api from "../axios/axiosInstance";
import URL from "../axios/URl";
import { setApiMessage } from "../components/common/Toaster";

function slugify(text) {
  return text
    .toString()
    .normalize("NFD")
    .replace(/\p{Diacritic}/gu, "")
    .replace(/[^\w\s-]/g, "")
    .trim()
    .replace(/\s+/g, "-")
    .replace(/-+/g, "-")
    .toLowerCase();
}

function Blog() {
  const [title, setTitle] = useState("");
  const [subtitle, setSubtitle] = useState("");
  const [content, setContent] = useState("");
  const [isPreview, setIsPreview] = useState(false);
  const [coverImage, setCoverImage] = useState(null);
  const [category, setCategory] = useState("");
  const [duration, setDuration] = useState("");
  const [date, setDate] = useState("");
  const [readTime, setReadTime] = useState("");
  const [keywords, setKeywords] = useState("");
  const [isPublishing, setIsPublishing] = useState(false);
  const [videoThumbnail, setVideoThumbnail] = useState(null);
  const editorRef = useRef(null);
  const htmlContentRef = useRef("");
  const [slug, setSlug] = useState("");
  const [slugAvailable, setSlugAvailable] = useState(null); // null = not checked, true = available, false = taken
  const slugCheckTimeout = useRef(null);

  // Initialize the editor with basic styling
  useEffect(() => {
    if (editorRef.current && !isPreview) {
      editorRef.current.innerHTML = content;
    }
  }, [isPreview]);

  // Drag-and-drop handlers for inserting images/videos
  useEffect(() => {
    const editor = editorRef.current;
    if (!editor) return;

    const handleDragOver = (e) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = "copy";
      editor.classList.add("ring-2", "ring-blue-400");
    };

    const handleDragLeave = (e) => {
      e.preventDefault();
      editor.classList.remove("ring-2", "ring-blue-400");
    };

    const handleDrop = (e) => {
      e.preventDefault();
      editor.classList.remove("ring-2", "ring-blue-400");
      const files = Array.from(e.dataTransfer.files);
      if (!files.length) return;
      files.forEach(async (file) => {
        if (file.type.startsWith("image")) {
          const reader = new FileReader();
          reader.onload = (ev) => {
            const img = `<div class="image-container"><img src="${ev.target.result}" alt="Blog image" style="max-width: 100%; height: auto; border-radius: 8px; margin: 16px 0;"/></div>`;
            document.execCommand("insertHTML", false, img);
            updateContent();
          };
          reader.readAsDataURL(file);
        } else if (file.type.startsWith("video")) {
          const reader = new FileReader();
          reader.onload = async (ev) => {
            const video = `<div class="video-container"><video controls style="max-width: 100%; border-radius: 8px; margin: 16px 0;"><source src="${ev.target.result}" type="${file.type}"></video></div>`;
            document.execCommand("insertHTML", false, video);
            updateContent();
            // Prompt for thumbnail
            setTimeout(() => {
              const input = document.createElement("input");
              input.type = "file";
              input.accept = "image/*";
              input.onchange = (thumbEvent) => {
                const thumbFile = thumbEvent.target.files[0];
                if (thumbFile) {
                  const thumbReader = new FileReader();
                  thumbReader.onload = (thumbEv) => {
                    const thumbImg = `<div class=\"video-thumbnail-container\"><img src=\"${thumbEv.target.result}\" alt=\"Video thumbnail\" style=\"max-width: 200px; height: auto; border-radius: 8px; margin: 8px 0;\"/></div>`;
                    document.execCommand("insertHTML", false, thumbImg);
                    updateContent();
                  };
                  thumbReader.readAsDataURL(thumbFile);
                }
              };
              alert(
                "You dropped a video. Please select a thumbnail image (optional)."
              );
              input.click();
            }, 100);
          };
          reader.readAsDataURL(file);
        }
      });
    };

    editor.addEventListener("dragover", handleDragOver);
    editor.addEventListener("dragleave", handleDragLeave);
    editor.addEventListener("drop", handleDrop);

    return () => {
      editor.removeEventListener("dragover", handleDragOver);
      editor.removeEventListener("dragleave", handleDragLeave);
      editor.removeEventListener("drop", handleDrop);
    };
  }, [isPreview]);

  const handleCoverImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setCoverImage({
          url: e.target.result,
          type: file.type.startsWith("image") ? "image" : "video",
          file: file,
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const insertImage = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*";
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const img = `<div class="image-container"><img src="${e.target.result}" alt="Blog image" style="max-width: 100%; height: auto; border-radius: 8px; margin: 16px 0;"/></div>`;
          document.execCommand("insertHTML", false, img);
          updateContent();
        };
        reader.readAsDataURL(file);
      }
    };
    input.click();
  };

  const insertLink = () => {
    const url = prompt("Enter URL:");
    const text = prompt("Enter link text:") || url;
    if (url) {
      const link = `<a href="${url}" target="_blank" style="color: #1a73e8; text-decoration: underline;">${text}</a>`;
      document.execCommand("insertHTML", false, link);
      updateContent();
    }
  };

  const formatText = (command, value = null) => {
    document.execCommand(command, false, value);
    updateContent();
    editorRef.current.focus();
  };

  const insertHeading = (level) => {
    const selection = window.getSelection();
    const selectedText = selection.toString() || "Heading";
    const headingStyles = {
      1: "font-size: 32px; font-weight: bold; margin: 24px 0 16px 0; line-height: 1.2;",
      2: "font-size: 24px; font-weight: bold; margin: 20px 0 12px 0; line-height: 1.3;",
      3: "font-size: 20px; font-weight: bold; margin: 16px 0 8px 0; line-height: 1.4;",
    };
    const heading = `<h${level} style="${headingStyles[level]}">${selectedText}</h${level}>`;
    document.execCommand("insertHTML", false, heading);
    updateContent();
  };

  const insertQuote = () => {
    const selection = window.getSelection();
    const selectedText = selection.toString() || "Quote text here...";
    const quote = `<blockquote style="border-left: 4px solid #1a73e8; padding-left: 16px; margin: 16px 0; font-style: italic; color: #666; background: #f8f9fa; padding: 16px; border-radius: 4px;">${selectedText}</blockquote>`;
    document.execCommand("insertHTML", false, quote);
    updateContent();
  };

  const insertCodeBlock = () => {
    const code = prompt("Enter code:") || 'console.log("Hello World");';
    const codeBlock = `<pre style="background: #f4f4f4; padding: 16px; border-radius: 8px; overflow-x: auto; font-family: 'Courier New', monospace; margin: 16px 0;"><code>${code}</code></pre>`;
    document.execCommand("insertHTML", false, codeBlock);
    updateContent();
  };

  const updateContent = () => {
    if (editorRef.current) {
      setContent(editorRef.current.innerHTML);
    }
  };

  const generateHTML = () => {
    const coverMediaHTML = coverImage
      ? coverImage.type === "image"
        ? `<div class="image-container"><img src="${coverImage.url}" alt="${title}" class="hero-image" /></div>`
        : `<div class="image-container"><video controls autoplay loop class="hero-image">
          <source src="${coverImage.url}" type="${
            coverImage.type === "video" ? "video/mp4" : ""
          }">
        </video></div>`
      : "";
    const fullHTML = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Metrophobic&display=swap" rel="stylesheet">
   <style>
        body {
            font-family: 'Metrophobic', sans-serif;
            background: #fff;
            font-size: 1rem; /* 16px base */
        }
        .content-container {
            color: #7D7D7D;
            max-width: 1100px;
            margin: 0 auto;
            line-height: 1.8;
            padding: 0 2em;
        }
        .content-container p {
            font-size: 1.15rem;
            margin-bottom: 1.5em;
        }
        .content-container ul,
        .content-container ol {
            font-size: 1.1rem;
            margin: 1.5em 0;
            padding-left: 1.5em;
        }
        .content-container li {
            margin-bottom: 0.75em;
        }
       /* Default <b> and <strong> styles (outside headings) */
.content-container b,
.content-container strong {
  color: #000000; /* Normal text color */
  font-weight: 500;
}

/* <b> or <strong> inside h1 */
.content-container h1 b,
.content-container h1 strong {
  font-size: 28px;
  color: #000000;
  font-weight: 600;
}

.image-container {
  border-radius: 32px;
}

/* <b> or <strong> inside h2 */
.content-container h2 b,
.content-container h2 strong {
  font-size: 24px;
  color: #000000;
  font-weight: 550;
}

/* <b> or <strong> inside h3 */
.content-container h3 b,
.content-container h3 strong {
  font-size: 20px;
  color: #000000;
  font-weight: 500;
}

        .content-container blockquote {
            font-size: 1.25rem;
            line-height: 1.6;
            border-left: 4px solid #FF7731;
            background: #f8f6f5;
            padding: 1.25em 1.5em;
            margin: 2em 0;
            border-radius: 0.5em;
            color: #000000;
        }
        .content-container pre {
            font-size: 1rem;
            line-height: 1.6;
            background: #f3f4f6;
            padding: 1em;
            border-radius: 0.5em;
            margin: 2em 0;
            overflow-x: auto;
        }
        .content-container img {
            margin: 2em auto;
            border-radius: 0.75em;
            max-width: 100%;
            height: auto;
            display: block;
        }
      .hero-image {
    width: 100%;
    max-width: 100%;
    aspect-ratio: 16 / 4.5; /* Reduced height */
    box-shadow: none;
    object-fit: cover;
    border-radius: 2em;
    display: block;
    margin: 0 auto;
    border: 1px solid #e5e7eb; /* Add this line */
    overflow: hidden;
}

/* Ensure video elements also have rounded corners */
.hero-image video {
    border-radius: 2em;
    overflow: hidden;
}

/* Ensure img elements also have rounded corners */
.hero-image img {
    border-radius: 2em;
    overflow: hidden;
}

        .meta-info {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75em;
            justify-content: center;
            margin: 1.5em 0 2em 0;
        }
        .meta-badge {
            background: #FF7731;
            color: #fff;
            padding: 0.4em 1.2em;
            border-radius: 999px;
            font-size: 0.95em;
            font-weight: 500;
            opacity: 0.92;
        }
        @media (max-width: 1024px) {
            .content-container {
                padding: 0 1em;
            }
            .hero-image {
                max-height: 350px;
            }
            .content-container p,
            .content-container ul,
            .content-container ol {
                font-size: 1.05rem;
            }
            .content-container h1 {
                font-size: 2em;
            }
            .content-container h2 {
                font-size: 1.4em;
            }
            .content-container h3 {
                font-size: 1.1em;
            }
        }
        @media (max-width: 768px) {
            .content-container {
                padding: 0 0.5em;
            }
            .content-container p,
            .content-container ul,
            .content-container ol {
                font-size: 0.98rem;
            }
            .content-container h1 {
                font-size: 1.4em;
            }
            .content-container h2 {
                font-size: 1.1em;
            }
            .content-container h3 {
                font-size: 1em;
            }
            .hero-image {
                max-height: 300px;
                object-fit: fill;

    max-width: 100%;
    aspect-ratio: 16 / 8.5; /* Reduced height */
                border-radius: 1em !important;
            }
        }
        @media (max-width: 480px) {
            .content-container {
                padding: 0 0.25em;
            }
            .content-container p,
            .content-container ul,
            .content-container ol {
                font-size: 0.92rem;
            }
            .content-container h1 {
                font-size: 1.1em;
            }
            .content-container h2 {
                font-size: 1em;
            }
            .content-container h3 {
                font-size: 0.95em;
            }
            .hero-image {
                 width: 100%;
    max-width: 100%;
    aspect-ratio: 16 / 8.5; /* Reduced height */
                border-radius: 0.7em !important;
            }
            .meta-info {
                flex-direction: column;
                align-items: stretch;
                gap: 0.4em;
            }
            .meta-badge {
                font-size: 0.85em;
                padding: 0.3em 0.8em;
            }
        }
    </style>
</head>
<body>
    <div class="min-h-screen bg-white flex flex-col">
        <!-- Hero Image Section (no text overlay) -->
       <section class="relative w-full overflow-hidden mb-0" style="min-height: 0;">
            ${coverMediaHTML}
        </section>
        <!-- Title, Subtitle, and Meta Info -->
        <div class="w-full  mx-auto px-8 text-center mt-8">
            <h1 class="text-4xl md:text-5xl font-extrabold mb-2" style="color:#000000; font-size:clamp(1.5rem,5vw,2.8rem); line-height:1.1;">${title}</h1>
            ${
              subtitle
                ? `<p class="text-lg md:text-xl text-gray-600 mb-2" style="font-size:clamp(1.05rem,3vw,1.5rem); line-height:1.3;">${subtitle}</p>`
                : ""
            }
            <div class="meta-info">
              ${category ? `<span class="meta-badge">${category}</span>` : ""}
              ${duration ? `<span class="meta-badge">${duration}</span>` : ""}
              ${date ? `<span class="meta-badge">${date}</span>` : ""}
              ${readTime ? `<span class="meta-badge">${readTime}</span>` : ""}
            </div>
        </div>
        <!-- Main Content -->
        <main class="flex-1 w-full  mx-auto px-6">
            <div class="content-container bg-white rounded-2xl  p-10 md:p-16 mb-10 ">
                ${content}
            </div>
        </main>
    </div>
</body>
</html>`;
    htmlContentRef.current = fullHTML;
    return fullHTML;
  };

  const downloadHTML = () => {
    const blob = new Blob([htmlContentRef.current], { type: "text/html" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${
      title.replace(/\s+/g, "-").toLowerCase() || "blog-post"
    }.html`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const copyHTML = () => {
    navigator.clipboard.writeText(htmlContentRef.current);
  };

  const createBlog = async () => {
    if (isPublishing) return;
    try {
      setIsPublishing(true);
      const htmlContent = generateHTML();
      const formData = new FormData();
      formData.append("title", title);
      formData.append("slug", slug);
      formData.append("keywords", keywords);
      formData.append("category", category);
      formData.append("read_time", readTime);

      // Handle cover media file upload
      if (coverImage && coverImage.file) {
        // Use the actual file object
        const fileName = coverImage.file.name;
        const fileExtension = fileName.split(".").pop();
        const mimeType = coverImage.file.type;
        formData.append(
          "banner",
          coverImage.file,
          `cover-media.${fileExtension}`
        );
      }
      // Handle thumbnail upload for both image and video
      if (videoThumbnail && videoThumbnail.file) {
        const fileName = videoThumbnail.file.name;
        const fileExtension = fileName.split(".").pop();
        formData.append(
          "video_thumbnail",
          videoThumbnail.file,
          `thumbnail.${fileExtension}`
        );
      }

      formData.append("content", htmlContent);
      const response = await api.post(URL.CREATE_BLOG, formData, {
        headers: { "Content-Type": "multipart/form-data" },
      });
      if (response.status) {
        setApiMessage("success", response?.message);
      }

      // Reset all fields on success

      setTitle("");
      setSubtitle("");
      setContent("");
      setCoverImage(null);
      setCategory("");
      setDuration("");
      setDate("");
      setReadTime("");
      setKeywords("");
      setIsPreview(false);
      setVideoThumbnail(null);
      if (editorRef.current) editorRef.current.innerHTML = "";
    } catch (error) {
      console.log("Failed to create blog. Please try again.");
    } finally {
      setIsPublishing(false);
    }
  };

  useEffect(() => {
    if (!title) {
      setSlug("");
      setSlugAvailable(null);
      return;
    }
    const newSlug = slugify(title);
    setSlug(newSlug);
    // Debounce API call
    if (slugCheckTimeout.current) clearTimeout(slugCheckTimeout.current);
    slugCheckTimeout.current = setTimeout(() => {
      if (!newSlug) return;
      api
        .get(`${URL.CHECK_SLUG}?slug=${encodeURIComponent(newSlug)}`)
        .then((res) => {
          setSlugAvailable(res.data?.status ?? false);
        })
        .catch(() => setSlugAvailable(false));
    }, 400);
    return () => {
      if (slugCheckTimeout.current) clearTimeout(slugCheckTimeout.current);
    };
  }, [title]);

  return (
    <div className="min-h-screen bg-white font-metrophobic">
      {/* Add Metrophobic font to the entire app */}
      <style jsx global>{`
        @import url("https://fonts.googleapis.com/css2?family=Metrophobic&display=swap");

        .editor-content {
          color: #00000099;
          font-size: 16px;
        }

        .editor-content b,
        .editor-content strong {
          color: #000000;
        }

        /* Hide filename in file inputs */
        input[type="file"]::file-selector-button {
          margin-right: 0;
        }

        input[type="file"]::-webkit-file-upload-button {
          margin-right: 0;
        }

        /* Hide the filename text */
        input[type="file"] {
          color: transparent;
        }

        input[type="file"]::before {
          content: none !important;
        }

        input[type="file"]::after {
          content: none !important;
        }
      `}</style>

      {/* Header */}
      <div className="border-b border-gray-200 bg-white sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-2xl font-bold text-gray-900">Blog Editor</h1>
            <div className="flex gap-3">
              <button
                onClick={() => setIsPreview(!isPreview)}
                className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Eye size={16} />
                {isPreview ? "Edit" : "Preview"}
              </button>
              {isPreview && (
                <>
                  <button
                    onClick={copyHTML}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Copy HTML
                  </button>
                  <button
                    onClick={downloadHTML}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Download HTML
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className=" mx-auto px-6 py-8">
        {!isPreview ? (
          <>
            {/* Updated Cover Media Upload Section */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Cover Media (Image or Video)
              </label>
              {!coverImage ? (
                <div
                  className="relative flex flex-col items-center justify-center w-full h-48 border-2 border-dashed border-gray-300 rounded-xl  transition-colors cursor-pointer group"
                  style={{ minHeight: "180px" }}
                  onClick={(e) => {
                    // Only trigger if the click is NOT on the input itself
                    if (e.target.id !== "cover-media-input") {
                      document.getElementById("cover-media-input").click();
                    }
                  }}
                  onDragOver={(e) => {
                    e.preventDefault();
                    e.currentTarget.classList.add(
                      "bg-blue-100",
                      "border-blue-600"
                    );
                  }}
                  onDragLeave={(e) => {
                    e.preventDefault();
                    e.currentTarget.classList.remove(
                      "bg-blue-100",
                      "border-blue-600"
                    );
                  }}
                  onDrop={(e) => {
                    e.currentTarget.classList.remove(
                      "bg-blue-100",
                      "border-blue-600"
                    );
                  }}
                >
                  <input
                    id="cover-media-input"
                    type="file"
                    accept="image/*,video/*"
                    onChange={handleCoverImageUpload}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                    tabIndex={-1}
                  />
                  <div className="flex flex-col items-center pointer-events-none select-none">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-12 w-12 text-gray-500 mb-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 16V4a1 1 0 011-1h8a1 1 0 011 1v12m-4 4h-4a1 1 0 01-1-1v-1m6 2a1 1 0 001-1v-1m-6 2a1 1 0 01-1-1v-1m0 0V4m0 12h6"
                      />
                    </svg>
                    <span className="text-gray-700 font-medium text-lg">
                      Drop image or video here, or{" "}
                      <span className="underline">click to upload</span>
                    </span>
                    <span className="text-gray-500 text-sm mt-1">
                      (JPG, PNG, GIF, MP4, etc.)
                    </span>
                  </div>
                </div>
              ) : (
                <div className="relative w-full">
                  {coverImage.type === "image" ? (
                    <img
                      src={coverImage.url}
                      alt="Cover preview"
                      className="w-full h-64 object-cover rounded-lg border"
                    />
                  ) : (
                    <video
                      src={coverImage.url}
                      controls
                      className="w-full h-64 object-cover rounded-lg border"
                    />
                  )}
                  <button
                    type="button"
                    className="absolute top-2 right-2 bg-white/80 hover:bg-white text-gray-700 border border-gray-300 rounded-full p-1 shadow"
                    onClick={() => {
                      setCoverImage(null);
                      setVideoThumbnail(null);
                    }}
                    title="Remove"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </div>
              )}
              {/* Thumbnail Upload */}
              {coverImage && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Thumbnail Image
                  </label>
                  {!videoThumbnail ? (
                    <div
                      className="relative flex flex-col items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg transition-colors cursor-pointer group"
                      style={{ minHeight: "90px" }}
                      onClick={(e) => {
                        // Only trigger if the click is NOT on the input itself
                        if (e.target.id !== "video-thumb-input") {
                          document.getElementById("video-thumb-input").click();
                        }
                      }}
                      onDragOver={(e) => {
                        e.preventDefault();
                        e.currentTarget.classList.add(
                          "bg-blue-100",
                          "border-blue-600"
                        );
                      }}
                      onDragLeave={(e) => {
                        e.preventDefault();
                        e.currentTarget.classList.remove(
                          "bg-blue-100",
                          "border-blue-600"
                        );
                      }}
                      onDrop={(e) => {
                        e.currentTarget.classList.remove(
                          "bg-blue-100",
                          "border-blue-600"
                        );
                      }}
                    >
                      <input
                        id="video-thumb-input"
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files[0];
                          if (file) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                              setVideoThumbnail({
                                url: e.target.result,
                                file: file,
                              });
                            };
                            reader.readAsDataURL(file);
                          }
                        }}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                        tabIndex={-1}
                      />
                      <div className="flex flex-col items-center pointer-events-none select-none">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-8 w-8 text-gray-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M7 16V4a1 1 0 011-1h8a1 1 0 011 1v12m-4 4h-4a1 1 0 01-1-1v-1m6 2a1 1 0 001-1v-1m-6 2a1 1 0 01-1-1v-1m0 0V4m0 12h6"
                          />
                        </svg>
                        <span className="text-gray-700 font-medium text-base">
                          Drop thumbnail here, or{" "}
                          <span className="underline">click to upload</span>
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="relative inline-block">
                      <img
                        src={videoThumbnail.url}
                        alt="Thumbnail preview"
                        className="w-32 h-20 object-cover rounded-lg border"
                      />
                      <button
                        type="button"
                        className="absolute top-1 right-1 bg-white/80 hover:bg-white text-gray-700 border border-gray-300 rounded-full p-0.5 shadow"
                        onClick={() => setVideoThumbnail(null)}
                        title="Remove"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                          />
                        </svg>
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Blog Meta Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <input
                  type="text"
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  placeholder="e.g., Technology, Design"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              {/* <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Duration
                </label>
                <input
                  type="text"
                  value={duration}
                  onChange={(e) => setDuration(e.target.value)}
                  placeholder="e.g., 5:30, 10 min read"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div> */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Date
                </label>
                <input
                  type="text"
                  value={date}
                  onChange={(e) => setDate(e.target.value)}
                  placeholder="e.g., March 15, 2024"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Read Time
                </label>
                <input
                  type="text"
                  value={readTime}
                  onChange={(e) => setReadTime(e.target.value)}
                  placeholder="e.g., 5 min read"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Keywords (comma separated)
                </label>
                <input
                  type="text"
                  value={keywords}
                  onChange={(e) => setKeywords(e.target.value)}
                  placeholder="e.g., react, web development, blog"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Title Input */}
            <div className="mb-6">
              <input
                type="text"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="Title"
                className="w-full text-4xl font-bold border-none outline-none placeholder-gray-400 resize-none"
              />
              {title && (
                <div className="mt-2 text-sm">
                  <span className="font-mono bg-gray-100 px-2 py-1 rounded">
                    {slug}
                  </span>
                  {slugAvailable === null ? null : slugAvailable ? (
                    <span className="ml-2 text-green-600">Slug available</span>
                  ) : (
                    <span className="ml-2 text-red-600">Slug taken</span>
                  )}
                </div>
              )}
            </div>

            {/* Subtitle Input */}
            <div className="mb-8">
              <input
                type="text"
                value={subtitle}
                onChange={(e) => setSubtitle(e.target.value)}
                placeholder="Subtitle (optional)"
                className="w-full text-xl text-gray-800 border-none outline-none placeholder-gray-400 resize-none"
              />
            </div>

            {/* Formatting Toolbar */}
            <div className="flex flex-wrap gap-2 p-4 bg-gray-50 rounded-lg mb-6 border">
              <button
                onClick={() => insertHeading(1)}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded transition-colors"
                title="Heading 1"
              >
                <Heading1 size={18} />
              </button>
              <button
                onClick={() => insertHeading(2)}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded transition-colors"
                title="Heading 2"
              >
                <Heading2 size={18} />
              </button>
              <button
                onClick={() => insertHeading(3)}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded transition-colors"
                title="Heading 3"
              >
                <Heading3 size={18} />
              </button>
              <div className="w-px h-8 bg-gray-300 mx-1"></div>
              <button
                onClick={() => formatText("bold")}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded transition-colors"
                title="Bold"
              >
                <Bold size={18} />
              </button>
              <button
                onClick={() => formatText("italic")}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded transition-colors"
                title="Italic"
              >
                <Italic size={18} />
              </button>
              <button
                onClick={() => formatText("underline")}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded transition-colors"
                title="Underline"
              >
                <Underline size={18} />
              </button>
              <div className="w-px h-8 bg-gray-300 mx-1"></div>
              {/* <button
                onClick={() => formatText("insertUnorderedList")}
                className="p-2 text-[#563D39] hover:text-gray-900 hover:bg-white rounded transition-colors"
                title="Bullet List"
              >
                <List size={18} />
              </button> */}
              {/* <button
                onClick={() => formatText("insertOrderedList")}
                className="p-2 text-[#563D39] hover:text-gray-900 hover:bg-white rounded transition-colors"
                title="Numbered List"
              >
                <ListOrdered size={18} />
              </button> */}
              <button
                onClick={insertQuote}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded transition-colors"
                title="Quote"
              >
                <Quote size={18} />
              </button>
              {/* <button
                onClick={insertCodeBlock}
                className="p-2 text-[#563D39] hover:text-gray-900 hover:bg-white rounded transition-colors"
                title="Code Block"
              >
                <Code size={18} />
              </button> */}
              <div className="w-px h-8 bg-gray-300 mx-1"></div>
              {/* <button
                onClick={insertLink}
                className="p-2 text-[#563D39] hover:text-gray-900 hover:bg-white rounded transition-colors"
                title="Link"
              >
                <Link size={18} />
              </button> */}
              <button
                onClick={insertImage}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white rounded transition-colors"
                title="Image"
              >
                <Image size={18} />
              </button>
            </div>

            {/* Content Editor */}
            <div
              ref={editorRef}
              contentEditable
              onInput={updateContent}
              onPaste={(e) => {
                e.preventDefault();
                const html = e.clipboardData.getData("text/html");
                const text = e.clipboardData.getData("text/plain");
                let content = html || text;
                if (html) {
                  // Create a temporary element to parse HTML
                  const tempDiv = document.createElement("div");
                  tempDiv.innerHTML = html;
                  // Recursively remove color and font-size from style attributes
                  const convertBoldAndClean = (node) => {
                    if (node.nodeType === 1) {
                      // Element node
                      // Skip images and preserve their styles
                      if (node.tagName !== "IMG") {
                        const fontWeight = node.style.fontWeight;
                        // Only convert if not already a bold tag
                        if (
                          fontWeight &&
                          /^(bold|700|800|900)$/.test(fontWeight) &&
                          node.tagName !== "B" &&
                          node.tagName !== "STRONG"
                        ) {
                          const b = document.createElement("b");
                          Array.from(node.attributes).forEach((attr) => {
                            if (attr.name !== "style") {
                              b.setAttribute(attr.name, attr.value);
                            }
                          });
                          while (node.firstChild) {
                            b.appendChild(node.firstChild);
                          }
                          node.parentNode.replaceChild(b, node);
                          node = b;
                        } else {
                          // Only remove specific styles instead of all styles
                          if (node.hasAttribute("style")) {
                            let style = node.getAttribute("style");
                            style = style
                              .replace(/color\s*:[^;]+;?/gi, "")
                              .replace(/font-size\s*:[^;]+;?/gi, "")
                              .replace(/;+/g, ";")
                              .replace(/^;|;$/g, "");

                            if (style) {
                              node.setAttribute("style", style);
                            } else {
                              node.removeAttribute("style");
                            }
                          }
                        }
                      }
                    }
                    Array.from(node.childNodes).forEach(convertBoldAndClean);
                  };
                  convertBoldAndClean(tempDiv);
                  content = tempDiv.innerHTML;
                }
                document.execCommand("insertHTML", false, content);
                // No need for further style enforcement, CSS will handle color and size
              }}
              className="editor-content min-h-96 p-6 text-lg leading-relaxed text-left border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              style={{
                lineHeight: "1.7",
              }}
              suppressContentEditableWarning={true}
              placeholder="Tell your story..."
            />

            {/* Publish Blog Button Only */}
            <div className="mt-8 flex justify-center gap-4">
              <button
                onClick={createBlog}
                disabled={!title.trim() || !content.trim() || isPublishing}
                className={`px-8 py-3 bg-brand-500 text-white rounded-lg font-medium transition-colors ${
                  isPublishing
                    ? "opacity-60 cursor-not-allowed"
                    : "hover:bg-brand-600"
                }`}
              >
                {isPublishing ? (
                  <span>
                    <svg
                      className="inline mr-2 w-5 h-5 animate-spin text-white"
                      fill="none"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                      ></path>
                    </svg>
                    Publishing...
                  </span>
                ) : (
                  "Publish Blog"
                )}
              </button>
            </div>
          </>
        ) : (
          /* Preview Mode */
          <div className="prose prose-lg max-w-none">
            <div className="min-h-screen bg-white">
              <div className="w-[90%] mx-auto px-4 py-6 mt-[90px] overflow-x-hidden">
                {/* Video Section */}
                <div className="relative mb-8">
                  <div className="relative w-full h-[500px] bg-gradient-to-br from-gray-800 via-gray-700 to-gray-600 rounded-2xl overflow-hidden">
                    {coverImage &&
                      (coverImage.type === "image" ? (
                        <img
                          src={coverImage.url}
                          alt={title}
                          className="w-full h-full object-cover opacity-60"
                        />
                      ) : (
                        <video
                          src={coverImage.url}
                          className="w-full h-full object-cover opacity-60"
                          autoPlay
                          loop
                        />
                      ))}

                    {/* Video overlay */}
                    <div className="absolute inset-0 bg-black/20" />

                    {/* Video controls */}
                    {/* <div className="absolute inset-0 flex items-center justify-center">
                      <div className="flex items-center gap-6">
                        <button className="w-12 h-12 bg-white/20 backdrop-blur rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all">
                          <RotateCcw className="w-5 h-5" />
                        </button>
                        <button className="w-16 h-16 bg-white/90 rounded-full flex items-center justify-center text-gray-800 hover:bg-white transition-all shadow-lg">
                          <Play className="w-6 h-6 ml-1" />
                        </button>
                        <button className="w-12 h-12 bg-white/20 backdrop-blur rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all">
                          <RotateCw className="w-5 h-5" />
                        </button>
                      </div>
                    </div> */}

                    {/* Video title overlay */}
                    <div className="absolute bottom-6 left-6">
                      <h1 className="text-white text-2xl font-bold mb-1">
                        {title}
                      </h1>
                      {category && (
                        <p className="text-white/80 text-sm">{category}</p>
                      )}
                    </div>

                    {/* Duration */}
                    {duration && (
                      <div className="absolute bottom-6 right-6">
                        <span className="text-white/90 text-sm font-medium bg-black/40 px-3 py-1 rounded-full backdrop-blur">
                          {duration}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Meta info */}
                <div className="flex items-center gap-4 mb-8 text-sm text-gray-600">
                  {date && <span>{date}</span>}
                  {readTime && <span>{readTime}</span>}
                </div>

                {/* Content Grid */}
                <div className="grid grid-cols-1">
                  {/* Main content */}
                  <div className="w-full">
                    <div className="space-y-6">
                      {subtitle && (
                        <p className="text-base leading-relaxed text-left">
                          {subtitle}
                        </p>
                      )}

                      <div
                        className="editor-content text-base leading-relaxed text-left w-full"
                        dangerouslySetInnerHTML={{ __html: content }}
                      />
                    </div>
                  </div>
                </div>
                <div className="flex flex-col md:flex-row justify-between items-center"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Blog;
