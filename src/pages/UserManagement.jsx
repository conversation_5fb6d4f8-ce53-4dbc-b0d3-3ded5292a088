import React, { useContext, useEffect, useState, useRef } from "react";
import { RiDeleteBin6Line } from "react-icons/ri";
import { format } from "date-fns";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { Dialog } from "@mui/material";
import { setApiMessage } from "../components/common/Toaster";
import Pagination from "../components/common/PaginationCommon";
import TableSkeleton, {
  CardSkeleton,
} from "../components/common/TableSkeleton";

// Base URL for API calls
const BASE_URL = "https://api.flowkar.com/api";

// API endpoints
const API_ENDPOINTS = {
  USER_MANAGEMENT: "/api-user/",
  ADMIN_USER_DELETE: "/delete-user-admin/",
};

// Mock data for missing variables (replace with actual data)
const localesData = {
  USER_WEB: {
    USER_MANAGEMENT: {
      USER_TITLE: "User Management",
      PROIFLE_IMAGE: "Profile Image",
      USERNAME: "Username",
      EMAIL: "Email",
      ACTIVE: "Active",
    },
    USER_PROFILE: {
      POST: "Posts",
      STATUS: "Status",
      ACTION: "Action",
    },
    DELETE: "Deleted",
    DELETE_USER_CONFIRMATION: "Are you sure you want to delete this user?",
    DELETE_POST_CONFIRMATION: "Are you sure you want to delete this post?",
    CANCEL: "Cancel",
    DELETED: "Deleted",
    DELETE: "Delete",
  },
};

const siteConstant = {
  SOCIAL_ICONS: {
    DUMMY_PROFILE: "./dummy_profile.svg",
    DELETE_POST_CONFIRMATION: "./delete_popup.svg",
  },
};

// DeletePostModel component
const DeletePostModel = ({
  open,
  handleDialogClose,
  post,
  fetchPosts,
  postDeleteURL,
}) => {
  const isUserDeletion = API_ENDPOINTS.ADMIN_USER_DELETE === postDeleteURL;
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `${BASE_URL}${postDeleteURL}?user_id=${post?.id}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete user");
      }

      const data = await response.json();

      if (data?.status) {
        setApiMessage("success", data?.messages || "User deleted successfully");
        setLoading(false);
        fetchPosts(1);
        handleDialogClose();
      } else {
        setApiMessage("error", data?.messages || "Failed to delete user");
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
      setApiMessage("error", error.message || "Failed to delete user");
      setLoading(false);
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleDialogClose}
        className="bg-transparent"
      >
        <div className="bg-white p-7 rounded-xl shadow-lg w-full max-w-md">
          <div className="flex flex-col gap-3 items-center">
            <div className="mb-4">
              <img
                src={siteConstant.SOCIAL_ICONS.DELETE_POST_CONFIRMATION}
                alt="Delete Image"
                className="w-26 h-26"
              />
            </div>
            <p className="text-[11px] sm:text-sm md:text-lg mb-4 whitespace-nowrap">
              {isUserDeletion
                ? localesData?.USER_WEB?.DELETE_USER_CONFIRMATION
                : localesData?.USER_WEB?.DELETE_POST_CONFIRMATION}
            </p>
            <div className="flex space-x-6 sm:space-x-8 md:space-x-12">
              <button
                className="text-[#563d39] py-1 sm:py-3 w-20 sm:w-24 md:w-28 lg:w-36 rounded-xl hover:bg-[#EFEBE9]  text-[13px] sm:text-[15px]  font-semibold border border-pink-100"
                onClick={() => handleDialogClose()}
              >
                {localesData?.USER_WEB?.CANCEL}
              </button>
              <button
                className="bg-[#563d39] text-white py-1 sm:py-3 w-20 sm:w-24 md:w-28 lg:w-36 text-[13px] sm:text-[15px] rounded-xl  font-semibold"
                onClick={() => handleDelete()}
                disabled={loading}
              >
                {localesData?.USER_WEB?.DELETE}
              </button>
            </div>
          </div>
        </div>
      </Dialog>
    </>
  );
};

const UserManagement = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 15;
  const [userlist, setUserlist] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [selectedUser, setselectedUser] = useState(null);
  const [additionalData, setAdditionalData] = useState({});
  const [dateRange, setDateRange] = useState([null, null]);
  const [searchText, setSearchText] = useState(""); // <-- NEW
  const [startDate, endDate] = dateRange;
  const containerRef = useRef(null);
  const cardsRef = useRef(null);
  const [isSearching, setIsSearching] = useState(false);

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const fetchUserManage = async (
    page,
    from = startDate,
    to = endDate,
    search = searchText
  ) => {
    try {
      //   const token = localStorage.getItem("token");
      let params = `?page=${page}`;
      if (from) {
        params += `&from_date=${format(new Date(from), "yyyy-MM-dd")}`;
      }
      if (to) {
        params += `&to_date=${format(new Date(to), "yyyy-MM-dd")}`;
      }
      if (search && search.trim() !== "") {
        params += `&search=${encodeURIComponent(search)}`;
      }

      const response = await fetch(
        `${BASE_URL}${API_ENDPOINTS.USER_MANAGEMENT}${params}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to fetch users");
      }

      const data = await response.json();
      const users = data?.results?.data || [];
      setUserlist(users);
      setTotalPages(Math.ceil(data?.count / itemsPerPage));
      setAdditionalData(data?.results?.additional_data || {});
    } catch (error) {
      console.error("Error fetching users:", error);
      setApiMessage("error", error.message || "Failed to fetch users");
    } finally {
      setLoading(false);
      setIsSearching(false);
    }
  };

  useEffect(() => {
    fetchUserManage(currentPage, startDate, endDate, searchText);
    // eslint-disable-next-line
  }, [currentPage]);

  // Scroll to top of cards after loading is done on page change
  useEffect(() => {
    if (!loading && cardsRef.current) {
      cardsRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [currentPage, loading]);

  const handlePrevious = () => {
    if (currentPage > 1) {
      setLoading(true);
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      setLoading(true);
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePageClick = (page) => {
    setLoading(true);
    setCurrentPage(page);
  };

  const handleDialogClose = () => setDeleteOpen(false);
  const handleClickOpen = (user) => {
    if (!user.is_deleted) {
      setselectedUser(user);
      setDeleteOpen(true);
    }
  };

  const handleDateRangeChange = (update) => {
    setDateRange(update);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    setIsSearching(true);
    setCurrentPage(1);
    fetchUserManage(1, startDate, endDate, searchText);
  };

  const handleSearchInputChange = (e) => {
    const value = e.target.value;
    setSearchText(value);

    // Call API when 2 or more characters are typed
    if (value.trim().length >= 2) {
      setIsSearching(true);
      setCurrentPage(1);
      fetchUserManage(1, startDate, endDate, value);
    } else if (value.trim() === "") {
      // Reset search when input is cleared
      setIsSearching(false);
      setCurrentPage(1);
      fetchUserManage(1, startDate, endDate, "");
    }
  };

  if (loading) {
    return (
      <div
        ref={mergeRefs(containerRef, cardsRef)}
        className="ps-3 pe-2 sm:px-4 sm:pt-4 overflow-auto bg-100"
      >
        {/* Header/Title Skeleton */}
        <div className="flex justify-between items-center">
          <div className="h-8 w-48 bg-gray-200 rounded mb-0 ms-1 mt-5 animate-pulse" />
        </div>
        {/* Additional Data Cards Skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mt-6 mb-8">
          {Array.from({ length: 6 }).map((_, idx) => (
            <div
              key={idx}
              className="bg-[#f9f9f9] rounded-xl shadow p-5 flex flex-col items-center animate-pulse"
            >
              <div className="h-8 w-16 bg-gray-200 rounded mb-2" />
              <div className="h-4 w-24 bg-gray-200 rounded mt-2" />
            </div>
          ))}
        </div>
        {/* Date Filter/Search Bar Skeleton */}
        <div className="flex flex-col md:flex-row gap-4 items-center mb-2 mt-4">
          <div className="w-full md:w-auto bg-[#f9f9f9] rounded-xl shadow p-4 flex flex-col md:flex-row items-center gap-4 border border-gray-100 animate-pulse">
            <div className="h-5 w-32 bg-gray-200 rounded mb-2 md:mb-0 md:mr-4" />{" "}
            {/* Label skeleton */}
            <div className="flex flex-col lg:flex-row gap-2 items-center">
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 pointer-events-none z-10">
                  <div className="w-5 h-5 bg-gray-300 rounded-full" />
                </span>
                <div className="border border-gray-300 rounded-md px-10 py-2 pr-10 bg-gray-200 w-36 h-10" />
              </div>
              <span className="mx-1 text-gray-300 font-semibold">to</span>
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-300 pointer-events-none z-10">
                  <div className="w-5 h-5 bg-gray-300 rounded-full" />
                </span>
                <div className="border border-gray-300 rounded-md px-10 py-2 pr-10 bg-gray-200 w-36 h-10" />
              </div>
            </div>
          </div>
          <div className="h-10 w-28 bg-gray-300 rounded-md animate-pulse" />
        </div>
        {/* End Date Filter/Search Bar Skeleton */}
        {/* Search Bar Skeleton */}
        <div className="flex flex-col md:flex-row gap-4 items-center mb-[30px] mt-[32px]">
          <div className="w-full md:w-72 h-10 bg-gray-200 rounded-md animate-pulse" />
          <div className="h-10 w-28 bg-gray-300 rounded-md animate-pulse" />
        </div>
        {/* Table Skeleton */}
        <TableSkeleton rows={itemsPerPage} />
        {/* Pagination Skeleton */}
        <div className="flex justify-end my-5 me-2 animate-pulse">
          <div className="flex items-center gap-2">
            {/* Prev button */}
            <div className="h-8 w-8 bg-gray-200 rounded-lg" />
            {/* Page numbers (simulate 5 pages + ... + last) */}
            <div className="h-8 w-8 bg-gray-200 rounded-lg" />
            <div className="h-8 w-8 bg-gray-200 rounded-lg" />
            <div className="h-8 w-8 bg-gray-200 rounded-lg" />
            <div className="h-8 w-8 bg-gray-200 rounded-lg" />
            <div className="h-8 w-8 bg-gray-200 rounded-lg" />
            <div className="h-8 w-8 bg-gray-200 rounded-lg" />
            {/* Next button */}
            <div className="h-8 w-8 bg-gray-200 rounded-lg" />
          </div>
        </div>
      </div>
    );
  }

  function mergeRefs(...refs) {
    return (el) => {
      refs.forEach((ref) => {
        if (typeof ref === "function") {
          ref(el);
        } else if (ref) {
          ref.current = el;
        }
      });
    };
  }

  return (
    <div
      ref={mergeRefs(containerRef, cardsRef)}
      className="ps-3 pe-2 sm:px-4 sm:pt-4 overflow-auto bg-100"
    >
      <div className="flex justify-between items-center">
        <h2 className="text-[16px] md:text-[18px] lg:text-[20px] font-bold mb-0 ms-1 pt-5">
          {localesData?.USER_WEB?.USER_MANAGEMENT?.USER_TITLE}
        </h2>
      </div>

      {/* End Search and Date Range Filter */}
      {/* Additional Data Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mt-6 mb-8">
        <div className="bg-[#f9f9f9] rounded-xl shadow p-5 flex flex-col items-center">
          <span className="text-2xl font-bold text-[563d39]">
            {additionalData.users_in_last_24h ?? 0}
          </span>
          <span className="text-gray-500 text-sm mt-2 text-center">
            Users in last 24h
          </span>
        </div>
        <div className="bg-[#f9f9f9] rounded-xl shadow p-5 flex flex-col items-center">
          <span className="text-2xl font-bold text-[563d39]">
            {additionalData.users_in_last_7d ?? 0}
          </span>
          <span className="text-gray-500 text-sm mt-2 text-center">
            Users in last 7 days
          </span>
        </div>
        <div className="bg-[#f9f9f9] rounded-xl shadow p-5 flex flex-col items-center">
          <span className="text-2xl font-bold text-[563d39]">
            {additionalData.users_in_last_30d ?? 0}
          </span>
          <span className="text-gray-500 text-sm mt-2 text-center">
            Users in last 30 days
          </span>
        </div>
        <div className="bg-[#f9f9f9] rounded-xl shadow p-5 flex flex-col items-center">
          <span className="text-2xl font-bold text-[563d39]">
            {additionalData.users_in_last_90d ?? 0}
          </span>
          <span className="text-gray-500 text-sm mt-2 text-center">
            Users in last 90 days
          </span>
        </div>
        <div className="bg-[#f9f9f9] rounded-xl shadow p-5 flex flex-col items-center">
          <span className="text-2xl font-bold text-[563d39]">
            {additionalData.users_in_last_180d ?? 0}
          </span>
          <span className="text-gray-500 text-sm mt-2 text-center">
            Users in last 180 days
          </span>
        </div>
        <div className="bg-[#f9f9f9] rounded-xl shadow p-5 flex flex-col items-center">
          <span className="text-2xl font-bold text-[563d39]">
            {additionalData.users_in_last_365d ?? 0}
          </span>
          <span className="text-gray-500 text-sm mt-2 text-center">
            Users in last 365 days
          </span>
        </div>
      </div>
      {/* Search and Date Range Filter */}
      {/* Date Range Filter */}
      <div className="flex flex-col md:flex-row gap-4 items-center mb-2 mt-4">
        <div className="w-full md:w-auto bg-[#f9f9f9] rounded-xl shadow p-4 flex flex-col md:flex-row items-center gap-4 border border-gray-100">
          <label className="block text-gray-700 font-semibold mb-2 md:mb-0 md:mr-4 text-sm md:text-base">
            Date Range Filter
          </label>
          <div className="flex flex-col lg:flex-row gap-2 items-center">
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none z-10">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6.75 3v2.25M17.25 3v2.25M3.75 7.5h16.5M4.5 21h15a.75.75 0 00.75-.75V7.5a.75.75 0 00-.75-.75h-15a.75.75 0 00-.75.75v12.75c0 .414.336.75.75.75z"
                  />
                </svg>
              </span>
              <DatePicker
                selected={startDate}
                onChange={(date) => handleDateRangeChange([date, endDate])}
                selectsStart
                startDate={startDate}
                endDate={endDate}
                placeholderText="Start Date"
                dateFormat="yyyy-MM-dd"
                className="border border-gray-300 rounded-md px-10 py-2 pr-10 focus:border-[#563d39] focus:ring-2 focus:ring-Red/20 transition-all duration-150 shadow-sm hover:border-Red"
                maxDate={endDate || null}
                isClearable
              />
            </div>
            <span className="mx-1 text-gray-500 font-semibold">to</span>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none z-10">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6.75 3v2.25M17.25 3v2.25M3.75 7.5h16.5M4.5 21h15a.75.75 0 00.75-.75V7.5a.75.75 0 00-.75-.75h-15a.75.75 0 00-.75.75v12.75c0 .414.336.75.75.75z"
                  />
                </svg>
              </span>
              <DatePicker
                selected={endDate}
                onChange={(date) => handleDateRangeChange([startDate, date])}
                selectsEnd
                startDate={startDate}
                endDate={endDate}
                placeholderText="End Date"
                dateFormat="yyyy-MM-dd"
                className="border border-gray-300 rounded-md px-10 py-2 pr-10 focus:border-[#563d39] focus:ring-2 focus:ring-Red/20 transition-all duration-150 shadow-sm hover:border-Red"
                minDate={startDate || null}
                isClearable
              />
            </div>
          </div>
        </div>
        <button
          onClick={() => {
            setLoading(true);
            setCurrentPage(1);
            fetchUserManage(1, startDate, endDate, searchText);
          }}
          className="bg-[#563d39] text-white px-6 py-2 rounded-md font-semibold focus:ring-2 focus:ring-Red/30 transition-all duration-150 shadow-md"
        >
          Filter by Date
        </button>
      </div>

      {/* Search Bar */}
      <div className="flex flex-col md:flex-row gap-4 items-center mb-2 mt-[32px]">
        <div className="w-full md:w-72 relative">
          <input
            type="text"
            value={searchText}
            onChange={handleSearchInputChange}
            placeholder="Search users..."
            className="w-full border border-gray-300 rounded-md px-10 py-2 pr-10 focus:border-[#563d39] focus:ring-2 focus:ring-Red/20 transition-all duration-150 shadow-sm hover:border-Red bg-[#f9f9f9] text-gray-700 placeholder:text-gray-400"
          />
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none z-10">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="w-5 h-5"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M21 21l-4.35-4.35m0 0A7.5 7.5 0 104.5 4.5a7.5 7.5 0 0012.15 12.15z"
              />
            </svg>
          </span>
        </div>
        <button
          onClick={handleSearchSubmit}
          className="bg-[#563d39] text-white px-6 py-2 rounded-md font-semibold focus:ring-2 focus:ring-Red/30 transition-all duration-150 shadow-md"
        >
          Search
        </button>
      </div>
      {/* End Additional Data Cards */}
      <div className="py-6">
        {isSearching ? (
          <TableSkeleton rows={itemsPerPage} />
        ) : (
          <>
            <div className="min-w-full w-full overflow-x-auto channel-scrollbar ">
              <table className="min-w-full w-full table-auto  text-center text-gray-500 dark:text-gray-400 border-separate border-spacing-y-4 ">
                <thead>
                  <tr className="bg-[#efebe9] text-[#563d39] whitespace-nowrap ps-6 rounded-md">
                    <th className="py-6 text-[14px] sm:text-[15px] ps-3">
                      S. No.
                    </th>
                    <th className="py-6 text-[14px] sm:text-[15px] ps-3">
                      User ID
                    </th>
                    <th className="py-8 text-[14px] sm:text-[15px] ps-3">
                      {localesData?.USER_WEB?.USER_MANAGEMENT?.PROIFLE_IMAGE}
                    </th>
                    <th className="text-[14px] sm:text-[15px] text-left ps-14">
                      {localesData?.USER_WEB?.USER_MANAGEMENT?.USERNAME}
                    </th>
                    <th className="text-[14px] sm:text-[15px] text-left ps-16">
                      {localesData?.USER_WEB?.USER_MANAGEMENT?.EMAIL}
                    </th>
                    <th className="text-[14px] sm:text-[15px] ">Date</th>
                    <th className="text-[14px] sm:text-[15px] ">
                      SM. Connected
                    </th>
                    <th className="text-[14px] sm:text-[15px] ">
                      {localesData?.USER_WEB?.USER_PROFILE?.POST}
                    </th>
                    <th className="text-[14px] sm:text-[15px] ">
                      {localesData?.USER_WEB?.USER_PROFILE?.STATUS}
                    </th>
                    <th className="text-[14px] sm:text-[15px]">
                      {localesData?.USER_WEB?.USER_PROFILE?.ACTION}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {userlist.map((user, index) => (
                    <tr
                      key={user.id}
                      className="border-b bg-[#f9f9f9] rounded-md hover:bg-primarBG"
                    >
                      <td className="p-5">
                        <p className="block antialiased text-sm leading-normal text-[#563d39] font-bold h-4 w-8">
                          {(currentPage - 1) * itemsPerPage + index + 1}
                        </p>
                      </td>
                      <td className="p-5">
                        <p className="block antialiased text-sm leading-normal text-[#563d39] font-bold h-4 w-8">
                          {user.id}
                        </p>
                      </td>
                      <td className="">
                        <div className="flex items-center justify-center gap-3 py-2">
                          <img
                            src={
                              user.profile_image ||
                              siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                            }
                            alt={user.username}
                            className="border-2 border-[#563d39] p-[5px] rounded-[20px] lg:rounded-[25px] object-cover h-14 w-14 sm:h-14 sm:w-14 lg:h-16 lg:w-16"
                          />
                        </div>
                      </td>
                      <td className="p-5 text-left">
                        <p className=" ps-10 block antialiased  text-sm leading-normal text-[#563d39] font-normal">
                          {user.username}
                        </p>
                      </td>
                      <td className="p-5 text-left">
                        <p className="block antialiased  text-sm leading-normal text-[#563d39] font-normal">
                          {user.email}
                        </p>
                      </td>
                      <td className="p-5">
                        <p className="block antialiased  text-sm leading-normal text-[#563d39] font-normal whitespace-nowrap">
                          {new Date(user.date).toLocaleDateString()}
                        </p>
                      </td>
                      <td className="p-5">
                        <p className="block antialiased  text-sm leading-normal text-[#563d39] font-normal whitespace-nowrap">
                          {user.connected_social_platforms}
                        </p>
                      </td>
                      <td className="p-5">
                        <p className="block antialiased  text-sm leading-normal text-[#563d39] font-normal whitespace-nowrap">
                          {user.posts}
                        </p>
                      </td>
                      <td className="p-5">
                        <p className="block antialiased   justify-center text-sm leading-normal text-[#563d39] font-normal whitespace-nowrap">
                          {user.is_deleted ? (
                            <span className="text-white font-semibold bg-[#f05252] px-5 py-2 rounded-xl text-center">
                              {localesData?.USER_WEB?.DELETED}
                            </span>
                          ) : (
                            <span className="text-white font-semibold bg-[#339f6e] px-5 py-2 rounded-xl justify-center">
                              {localesData?.USER_WEB?.USER_MANAGEMENT?.ACTIVE}
                            </span>
                          )}
                        </p>
                      </td>
                      <td className="py-4 flex justify-center gap-4 px-5">
                        {!user.is_deleted && (
                          <div
                            className="bg-gray-50 rounded-md flex justify-center items-center text-[#563d39] h-12 w-12 hover:bg-gray-100 cursor-pointer"
                            onClick={() => handleClickOpen(user)}
                            aria-disabled={user?.is_deleted}
                          >
                            <RiDeleteBin6Line
                              className="text-[#563d39]"
                              style={{ height: "25px", width: "28px" }}
                            />
                          </div>
                        )}
                        {/* <div
                          className="bg-gray-100 rounded-md flex justify-center items-center h-12 w-12 hover:bg-profileCardBGcursor-pointer"
                          onClick={handleClick}
                        >
                          <BsThreeDotsVertical
                            style={{ height: "25px", width: "20px" }}
                          />
                        </div> */}
                        {/* <Popover
                          id={id}
                          open={open}
                          anchorEl={anchorEl}
                          onClose={handleClose}
                          anchorOrigin={{
                            vertical: "bottom",
                            horizontal: "left",
                          }}
                          PaperProps={{
                            sx: {
                              boxShadow: "1px 1px 3px rgba(255, 242, 242)",
                              backgroundColor: "white",
                              borderRadius: "10px",
                            },
                          }}
                        >
                          <div className="flex items-center gap-2 pe-4 p-1 shadow-2xl ">
                            <Android12Switch />
                            <Typography sx={{ p: 1 }}>Banned</Typography>
                          </div>
                        </Popover> */}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="mb-20">
              <Pagination
                totalPages={totalPages}
                currentPage={currentPage}
                handlePrevious={handlePrevious}
                handleNext={handleNext}
                handlePageClick={handlePageClick}
              />
            </div>
          </>
        )}
      </div>
      <DeletePostModel
        open={deleteOpen}
        handleDialogClose={handleDialogClose}
        post={selectedUser}
        fetchPosts={fetchUserManage}
        postDeleteURL={API_ENDPOINTS.ADMIN_USER_DELETE}
      />
    </div>
  );
};

export default UserManagement;
