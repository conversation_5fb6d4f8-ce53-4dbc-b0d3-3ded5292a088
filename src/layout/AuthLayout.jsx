import { Link } from "react-router-dom";
import { useTheme } from "../context/ThemeContext";
import { ThemeToggleButton } from "../components/common/ThemeToggleButton";
import logo from "../../public/logo.svg";

const AuthLayout = ({ children }) => {
  const { theme } = useTheme();

  return (
    <div className="flex min-h-screen bg-[#FFFFFF] text-[#344054]  dark:text-[#FFFFFF]">
      {/* Left side - Form container */}
      <div className="flex flex-col w-full lg:w-1/2 justify-center px-5 sm:px-10 md:px-16 lg:px-20 py-10">
        <div className="w-full max-w-md mx-auto">{children}</div>
      </div>

      {/* Right side - Brand showcase */}
      <div className="hidden lg:flex lg:w-1/2 bg-[#f9f8f2] flex-col items-center justify-center relative">
        <div className="flex flex-col justify-center text-center px-12 max-w-lg">
          <div className="flex justify-center items-center mb-8">
          </div>
            <img src={logo} alt="Flexion" className="h-[150px] mb-5"/>
          <h1 className="text-3xl md:text-4xl font-semibold text-[#000000] mb-4">
            Flexion Dashboard
          </h1>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
