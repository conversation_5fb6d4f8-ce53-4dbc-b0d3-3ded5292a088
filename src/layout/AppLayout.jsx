import { SidebarProvider, useSidebar } from "../context/SidebarContext";
import { Outlet } from "react-router";
import AppHeader from "./AppHeader";
import Backdrop from "./Backdrop";
import AppSidebar from "./AppSidebar";
import { useLocation } from "react-router-dom";

const LayoutContent = () => {
  const { isExpanded, isHovered, isMobileOpen, toggleMobileSidebar } =
    useSidebar();
  const location = useLocation();

  const isUserManagement = location.pathname.startsWith("/user-management");

  return (
    <div className="min-h-screen xl:flex">
      <AppSidebar />
      <Backdrop />
      {/* Floating Sidebar Toggle Button for Mobile */}
      {!isMobileOpen && (
        <button
          className="fixed bottom-6 left-6 z-[100] p-3 rounded-full bg-white shadow-lg border border-gray-200 dark:bg-brand-950 dark:border-brand-800 lg:hidden transition hover:bg-gray-100 dark:hover:bg-brand-900"
          onClick={toggleMobileSidebar}
          aria-label="Open Sidebar"
        >
          {/* Hamburger Icon */}
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              x="4"
              y="6"
              width="16"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect
              x="4"
              y="11"
              width="16"
              height="2"
              rx="1"
              fill="currentColor"
            />
            <rect
              x="4"
              y="16"
              width="16"
              height="2"
              rx="1"
              fill="currentColor"
            />
          </svg>
        </button>
      )}
      <div
        className={`flex-1 transition-all duration-300 ease-in-out ${
          isExpanded || isHovered ? "lg:ml-[290px]" : "lg:ml-[90px]"
        } ${isMobileOpen ? "ml-0" : ""}`}
      >
        <div
          className={`p-4 mx-auto ${
            !isUserManagement ? "max-w-[--breakpoint-2xl]" : ""
          } md:p-6`}
        >
          <Outlet />
        </div>
      </div>
    </div>
  );
};

const AppLayout = () => {
  return (
    <SidebarProvider>
      <LayoutContent />
    </SidebarProvider>
  );
};

export default AppLayout;
