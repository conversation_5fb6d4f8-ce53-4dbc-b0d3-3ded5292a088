import axios from "axios";
import { setApiMessage } from "../components/common/Toaster";
import URL from "./URl";

const api = axios.create({
    baseURL: "https://flexioninfotech.com/api",
    
    
});
api.interceptors.request.use((config) => {
    const token = localStorage.getItem("token");

    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

const TOAST_ENDPOINTS = [URL.CREATE_BLOG, URL.DELETE_BLOG, URL.SIGN_IN];

function shouldShowToast(config) {
  if (!config || !config.url) return false;
  const url = config.url.split('?')[0];
  return TOAST_ENDPOINTS.includes(url);
}

api.interceptors.response.use(
  (response) => {
    if (shouldShowToast(response.config) && response?.data?.message) {
      setApiMessage("success", response.data.message);
    }
    return response;
  },
  (error) => {
    if (shouldShowToast(error.config)) {
      const message =
        error?.response?.data?.detail ||
        error?.response?.data?.message ||
        error?.message ||
        "An error occurred";
      setApiMessage("error", message);
    }
    return Promise.reject(error);
  }
);

export default api;
