import { createContext, useState, useContext, useEffect } from "react";
import api from "../axios/axiosInstance";

const UserContext = createContext(undefined);

export const UserProvider = ({ children }) => {
  const [user, setUser] = useState({
    first_name: "",
    last_name: "",
    email: "",
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const refreshUserData = async () => {
    try {
      setLoading(true);
      // Basic user data can be fetched from token or session
      // For now, we'll keep the basic structure without profile API calls
      setError(null);
      return { success: true };
    } catch (err) {
      console.error("Error fetching user data:", err);
      setError("Failed to load user data");
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refreshUserData();
  }, []);

  const updateUser = async (userData) => {
    try {
      setLoading(true);

      // Update local state immediately for instant UI feedback
      setUser((prevUser) => ({
        ...prevUser,
        ...userData,
      }));

      setError(null);
      return { success: true, user: userData };
    } catch (err) {
      console.error("Error updating user data:", err);
      setError("Failed to update user data");
      return {
        success: false,
        error: err.response?.data?.detail || err.message,
      };
    } finally {
      setLoading(false);
    }
  };

  // Add a function to refresh all data at once
  const refreshAllData = async () => {
    try {
      setLoading(true);
      await refreshUserData();
      return { success: true };
    } catch (err) {
      console.error("Error refreshing data:", err);
      setError("Failed to refresh data");
      return { success: false, error: err.message };
    } finally {
      setLoading(false);
    }
  };

  return (
    <UserContext.Provider
      value={{
        user,
        updateUser,
        refreshUserData,
        refreshAllData,
        loading,
        error,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider");
  }
  return context;
};
